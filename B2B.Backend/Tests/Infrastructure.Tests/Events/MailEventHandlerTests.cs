using Moq;
using Microsoft.Extensions.Logging;
using MassTransit;
using Core.Events;
using PanelApi.Events;

namespace Infrastructure.Tests.Events;

public class MailEventHandlerTests
{
    private readonly Mock<ILogger<CustomerShipmentNotificationHandler>> _mockCustomerLogger;
    private readonly Mock<IPublishEndpoint> _mockPublishEndpoint;

    public MailEventHandlerTests()
    {
        _mockCustomerLogger = new Mock<ILogger<CustomerShipmentNotificationHandler>>();
        _mockPublishEndpoint = new Mock<IPublishEndpoint>();
    }

    [Fact]
    public async Task CustomerShipmentNotificationHandler_WithKaanSaliEmail_PublishesSendMailRequested()
    {
        // Arrange
        var handler = new CustomerShipmentNotificationHandler(_mockPublishEndpoint.Object, _mockCustomerLogger.Object);

        var notification = new CustomerShipmentNotificationRequested
        {
            ShipmentId = Guid.CreateVersion7(),
            OrderId = Guid.CreateVersion7(),
            CustomerEmail = "<EMAIL>", // Test email
            CustomerName = "Kaan Sali",
            OrderNumber = "ORD-12345",
            CarrierName = "Yurtiçi Kargo",
            TrackingNumber = "YK123456789",
            TrackingUrl = "https://kargo.yurtici.com.tr/tracking/YK123456789",
            DeliveryAddress = "Test Address, Test City",
            EstimatedDeliveryDate = DateTime.UtcNow.AddDays(3)
        };

        var mockContext = new Mock<ConsumeContext<CustomerShipmentNotificationRequested>>();
        mockContext.Setup(c => c.Message).Returns(notification);

        // Act
        await handler.Consume(mockContext.Object);

        // Assert
        _mockPublishEndpoint.Verify(p => p.Publish(
            It.Is<SendMailRequested>(smr =>
                smr.TemplateShortCode == "customer-shipment-notification" &&
                smr.ToEmail == "<EMAIL>" &&
                smr.ToName == "Kaan Sali" &&
                smr.Variables.ContainsKey("customerName") &&
                smr.Variables["customerName"] == "Kaan Sali" &&
                smr.Variables.ContainsKey("orderNumber") &&
                smr.Variables["orderNumber"] == "ORD-12345" &&
                smr.Variables.ContainsKey("carrierName") &&
                smr.Variables["carrierName"] == "Yurtiçi Kargo" &&
                smr.Variables.ContainsKey("trackingNumber") &&
                smr.Variables["trackingNumber"] == "YK123456789"
            ),
            It.IsAny<CancellationToken>()
        ), Times.Once);
    }

}
