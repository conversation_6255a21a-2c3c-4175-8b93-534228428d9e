using Core.Enums;

namespace Application.Contracts.DTOs;

public class OrderDto
{
    public Guid Id { get; set; }
    public Guid CustomerId { get; set; }
    public Guid? AddressId { get; set; }
    public string OrderNumber { get; set; } = null!;
    public OrderStatus Status { get; set; }
    public decimal TotalAmount { get; set; }
    public decimal DiscountAmount { get; set; }
    public decimal ShippingAmount { get; set; }
    public decimal TaxAmount { get; set; }
    public string? Notes { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    
    // Navigation properties
    public CustomerDto? Customer { get; set; }
    public AddressDto? Address { get; set; }
    public List<OrderRowDto> OrderRows { get; set; } = [];
    public List<PaymentDto> Payments { get; set; } = [];
    public List<ShipmentDto> Shipments { get; set; } = [];
}

public class OrderListDto
{
    public Guid Id { get; set; }
    public string OrderNumber { get; set; } = null!;
    public OrderStatus Status { get; set; }
    public decimal TotalAmount { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CustomerName { get; set; } = null!;
    public int ItemCount { get; set; }
}

public class OrderCreateDto
{
    public Guid CustomerId { get; set; }
    public Guid? AddressId { get; set; }
    public decimal DiscountAmount { get; set; } = 0;
    public decimal ShippingAmount { get; set; } = 0;
    public decimal TaxAmount { get; set; } = 0;
    public string? Notes { get; set; }
    public List<OrderRowCreateDto> OrderRows { get; set; } = [];
}

public class OrderUpdateDto
{
    public Guid Id { get; set; }
    public OrderStatus Status { get; set; }
    public decimal DiscountAmount { get; set; }
    public decimal ShippingAmount { get; set; }
    public decimal TaxAmount { get; set; }
    public string? Notes { get; set; }
}

public class OrderStatusUpdateDto
{
    public Guid Id { get; set; }
    public OrderStatus Status { get; set; }
    public string? Notes { get; set; }
}

public class OrderRowDto
{
    public Guid Id { get; set; }
    public Guid OrderId { get; set; }
    public Guid ProductId { get; set; }
    public int Quantity { get; set; }
    public decimal Price { get; set; }
    public decimal DiscountedPrice { get; set; }
    public ProductDto? Product { get; set; }
}

public class OrderRowCreateDto
{
    public Guid ProductId { get; set; }
    public int Quantity { get; set; }
    public decimal Price { get; set; }
    public decimal DiscountedPrice { get; set; }
}
