using System.ComponentModel.DataAnnotations;

namespace Application.Contracts.DTOs;

public class CartDto
{
    public Guid Id { get; set; }
    public Guid CustomerId { get; set; }
    public string CustomerName { get; set; } = null!;
    public List<CartItemDto> Items { get; set; } = [];
    public int TotalItems { get; set; }
    public decimal TotalAmount { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class CartItemDto
{
    public Guid Id { get; set; }
    public Guid CartId { get; set; }
    public Guid ProductId { get; set; }
    public string ProductName { get; set; } = null!;
    public string ProductSku { get; set; } = null!;
    public decimal Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal TotalPrice { get; set; }
    public int StockQuantity { get; set; }
    public bool IsInStock { get; set; }
    public string? ProductImage { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class CartListDto
{
    public Guid Id { get; set; }
    public Guid CustomerId { get; set; }
    public string CustomerName { get; set; } = null!;
    public string? CustomerEmail { get; set; }
    public int ItemCount { get; set; }
    public decimal TotalAmount { get; set; }
    public DateTime LastUpdated { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class CartItemCreateDto
{
    [Required]
    public Guid CartId { get; set; }

    [Required]
    public Guid ProductId { get; set; }

    [Required]
    [Range(0.01, double.MaxValue, ErrorMessage = "Miktar 0'dan büyük olmalıdır")]
    public decimal Quantity { get; set; }

    [Required]
    [Range(0.01, double.MaxValue, ErrorMessage = "Birim fiyat 0'dan büyük olmalıdır")]
    public decimal UnitPrice { get; set; }
}

public class CartItemUpdateDto
{
    [Required]
    public Guid Id { get; set; }

    [Required]
    [Range(0.01, double.MaxValue, ErrorMessage = "Miktar 0'dan büyük olmalıdır")]
    public decimal Quantity { get; set; }

    [Required]
    [Range(0.01, double.MaxValue, ErrorMessage = "Birim fiyat 0'dan büyük olmalıdır")]
    public decimal UnitPrice { get; set; }
}

public class CartSummaryDto
{
    public Guid CartId { get; set; }
    public Guid CustomerId { get; set; }
    public int TotalItems { get; set; }
    public decimal TotalAmount { get; set; }
    public int UniqueProducts { get; set; }
    public DateTime LastActivity { get; set; }
}

public class CartAnalyticsDto
{
    public int TotalActiveCarts { get; set; }
    public int TotalAbandonedCarts { get; set; }
    public decimal AverageCartValue { get; set; }
    public decimal TotalCartValue { get; set; }
    public int TotalCartItems { get; set; }
    public List<CartTopProductsDto> TopProducts { get; set; } = [];
    public List<CartByDateDto> CartsByDate { get; set; } = [];
}

public class CartTopProductsDto
{
    public Guid ProductId { get; set; }
    public string ProductName { get; set; } = null!;
    public string ProductSku { get; set; } = null!;
    public int TimesAddedToCart { get; set; }
    public decimal TotalQuantity { get; set; }
    public decimal TotalValue { get; set; }
}

public class CartByDateDto
{
    public DateTime Date { get; set; }
    public int CartCount { get; set; }
    public decimal TotalValue { get; set; }
}

public class BulkCartItemDeleteDto
{
    [Required]
    public List<Guid> CartItemIds { get; set; } = [];
}

public class ClearCartDto
{
    [Required]
    public Guid CartId { get; set; }
}
