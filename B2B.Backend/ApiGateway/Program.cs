using Ocelot.DependencyInjection;
using Ocelot.Middleware;
using Microsoft.IdentityModel.Tokens;
using System.Text;
var builder = WebApplication.CreateBuilder(args);

builder.WebHost.ConfigureKestrel(options =>
{
    options.ListenAnyIP(33800); // ApiGateway portu
});

builder.Configuration.AddJsonFile("ocelot.json", optional: false, reloadOnChange: true);

// JWT Authentication - Panel ve Web için ayrı scheme'ler
builder.Services.AddAuthentication()
    .AddJwtBearer("PanelJwtBearer", options =>
    {
        options.RequireHttpsMetadata = false; // Development için false
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = false, // NextAuth token'ları için false
            ValidateAudience = false,
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(builder.Configuration["NextAuth:SecretPanel"] ?? "cok-gizli-key")),
            ValidateLifetime = true,
            ClockSkew = TimeSpan.FromMinutes(5), // Daha toleranslı
            NameClaimType = "name",
            RoleClaimType = "roles"
        };

        // Token'ı debug etmek için
        options.Events = new Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerEvents
        {
            OnAuthenticationFailed = context =>
            {
                Console.WriteLine($"ApiGateway Panel Authentication failed: {context.Exception.Message}");
                return Task.CompletedTask;
            },
            OnTokenValidated = context =>
            {
                Console.WriteLine($"ApiGateway Panel Token validated for user: {context.Principal?.Identity?.Name}");
                return Task.CompletedTask;
            }
        };
    })
    .AddJwtBearer("WebJwtBearer", options =>
    {
        options.RequireHttpsMetadata = false; // Development için false
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = false, // NextAuth token'ları için false
            ValidateAudience = false,
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(builder.Configuration["NextAuth:SecretWeb"] ?? "cok-gizli-key")),
            ValidateLifetime = true,
            ClockSkew = TimeSpan.FromMinutes(5), // Daha toleranslı
            NameClaimType = "name",
            RoleClaimType = "roles"
        };

        // Token'ı debug etmek için
        options.Events = new Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerEvents
        {
            OnAuthenticationFailed = context =>
            {
                Console.WriteLine($"ApiGateway Web Authentication failed: {context.Exception.Message}");
                return Task.CompletedTask;
            },
            OnTokenValidated = context =>
            {
                Console.WriteLine($"ApiGateway Web Token validated for user: {context.Principal?.Identity?.Name}");
                return Task.CompletedTask;
            }
        };
    });
// CORS ayarları
var corsOrigins = builder.Configuration.GetSection("CorsHosts").Get<string[]>();
Console.WriteLine($"ApiGateway CORS Origins: {string.Join(", ", corsOrigins ?? new[] { "none" })}");

builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowFrontend", policy =>
    {
        policy.WithOrigins(corsOrigins!)
            .AllowAnyHeader()
            .AllowAnyMethod()
            .AllowCredentials()
            .SetPreflightMaxAge(TimeSpan.FromMinutes(10));
    });
});
// Add services to the container.
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();
builder.Services.AddOcelot();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}


app.UseHttpsRedirection();
app.UseCors("AllowFrontend");
app.UseAuthentication();

await app.UseOcelot();

app.Run();

