using Core.Events;
using MassTransit;
using Microsoft.Extensions.Logging;

namespace PanelApi.Events;

/// <summary>
/// Müşteri kargo bildirim maili event handler'ı
/// MailAPI'ye SendMailRequested event'i gönderir
/// </summary>
public class CustomerShipmentNotificationHandler : IConsumer<CustomerShipmentNotificationRequested>
{
    private readonly IPublishEndpoint _publishEndpoint;
    private readonly ILogger<CustomerShipmentNotificationHandler> _logger;

    public CustomerShipmentNotificationHandler(
        IPublishEndpoint publishEndpoint,
        ILogger<CustomerShipmentNotificationHandler> logger)
    {
        _publishEndpoint = publishEndpoint;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<CustomerShipmentNotificationRequested> context)
    {
        var notification = context.Message;

        try
        {
            _logger.LogInformation("Processing customer shipment notification for: {CustomerEmail}",
                notification.CustomerEmail);

            // MailAPI'ye SendMailRequested event'i gönder
            await _publishEndpoint.Publish(new SendMailRequested
            {
                TemplateShortCode = "customer-shipment-notification",
                ToEmail = notification.CustomerEmail,
                ToName = notification.CustomerName,
                Variables = new Dictionary<string, string>
                {
                    { "customerName", notification.CustomerName },
                    { "orderNumber", notification.OrderNumber },
                    { "carrierName", notification.CarrierName },
                    { "trackingNumber", notification.TrackingNumber },
                    { "trackingUrl", notification.TrackingUrl ?? "" },
                    { "deliveryAddress", notification.DeliveryAddress },
                    { "estimatedDelivery", notification.EstimatedDeliveryDate?.ToString("dd.MM.yyyy") ?? "2-3 iş günü içinde" }
                },
                Priority = 2, // Normal öncelik
                RelatedEntityId = notification.ShipmentId,
                RelatedEntityType = "Shipment"
            });

            _logger.LogInformation("SendMailRequested event published for customer: {CustomerEmail}",
                notification.CustomerEmail);

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing customer shipment notification for: {CustomerEmail}",
                notification.CustomerEmail);
            throw;
        }
    }
}
