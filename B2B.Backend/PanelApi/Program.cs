using System.Text;
using Application.Contracts.Interfaces;
using Core.Entities;
using Core.Interfaces;
using Infrastructure.Data;
using Infrastructure.Extensions;
using Infrastructure.Identity;
using Infrastructure.Repositories;
using Infrastructure.Services;
using Infrastructure.Data.Repositories;
using Infrastructure.Data.Repositories.Interfaces;
using Infrastructure.Seeders;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.Extensions.Logging;
using PanelApi.Extensions;
var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
var configuration = builder.Configuration;
builder.Services
    .AddOpenApi()
    .AddDbContext<B2BDbContext>(options =>
    options.UseNpgsql(configuration.GetConnectionString("VeriTabani")));

builder.Services.AddIdentity<B2BUser, B2BRole>()
    .AddDefaultTokenProviders();

// Register custom user and role stores with specific DbContext type
builder.Services.AddScoped<IUserStore<B2BUser>>(provider =>
    new UuidV7UserStore(provider.GetRequiredService<B2BDbContext>()));
builder.Services.AddScoped<IRoleStore<B2BRole>>(provider =>
    new UuidV7RoleStore(provider.GetRequiredService<B2BDbContext>()));

builder.Services.AddCors(configuration);

// MassTransit konfigürasyonu
builder.Services.AddMassTransitConfiguration(configuration);

// 2) JWT Bearer Authentication
builder.Services.AddAuthentication(options =>
    {
        options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
        options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    })
    .AddJwtBearer(options =>
    {
        options.RequireHttpsMetadata = false; // Development için false
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = false, // NextAuth token'ları için false
            ValidateAudience = false,
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(builder.Configuration["NextAuth:Secret"]!)),
            ValidateLifetime = true,
            ClockSkew = TimeSpan.FromMinutes(5), // Daha toleranslı
            NameClaimType = "name",
            RoleClaimType = "roles"
        };

        // Token'ı debug etmek için
        options.Events = new JwtBearerEvents
        {
            OnAuthenticationFailed = context =>
            {
                Console.WriteLine($"Authentication failed: {context.Exception.Message}");
                return Task.CompletedTask;
            },
            OnTokenValidated = context =>
            {
                Console.WriteLine($"Token validated for user: {context.Principal?.Identity?.Name}");
                return Task.CompletedTask;
            }
        };
    });

// Politika tabanlı yetkilendirme ekleyin
builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("RequireAdmin", policy => policy.RequireClaim("roles", "Admin"));
    options.AddPolicy("RequireUser", policy => policy.RequireClaim("roles", "User"));

    // Temel yetki sabitleri
    var resources = new[] {
        "product", "product_categories", "product_brands", "product_attributes", "product_reviews",
        "order", "customer", "role", "user", "payment", "shipping", "mail", "settings", "dashboard", "erp", "coupon"
    };
    var actions = new[] { PermissionExtensions.READ, PermissionExtensions.CREATE,
                          PermissionExtensions.UPDATE, PermissionExtensions.DELETE };

    // Dinamik politikalar oluştur
    foreach (var resource in resources)
    {
        foreach (var action in actions)
        {
            options.AddPolicy($"{resource}_{action}", policy =>
                policy.RequireAssertion(context =>
                    context.User.HasPermission(resource, action)));
        }
    }
});

builder.Services.AddHttpContextAccessor();
builder.Services.AddScoped<ICurrentUserService, CurrentUserService>();
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddScoped<IProductService, ProductService>();
builder.Services.AddScoped<IProductCategoryService, ProductCategoryService>();
builder.Services.AddScoped<IProductBrandService, ProductBrandService>();
builder.Services.AddScoped<IProductAttributeService, ProductAttributeService>();
builder.Services.AddScoped<IProductAttributeValueService, ProductAttributeValueService>();
builder.Services.AddScoped<IProductAttributeMappingService, ProductAttributeMappingService>();
builder.Services.AddScoped<IProductImageService, ProductImageService>();
builder.Services.AddScoped<IProductFaqService, ProductFaqService>();
builder.Services.AddScoped<IProductSeoService, ProductSeoService>();
builder.Services.AddScoped<IProductReviewService, ProductReviewService>();
builder.Services.AddScoped<IProductVariantGeneratorService, ProductVariantGeneratorService>();
builder.Services.AddScoped<IRoleService, RoleService>();
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<IPermissionResourceService, PermissionResourceService>();
builder.Services.AddScoped<IOrderService, OrderService>();
builder.Services.AddScoped<IPaymentService, PaymentService>();
builder.Services.AddScoped<IShipmentService, ShipmentService>();
builder.Services.AddScoped<ICustomerService, CustomerService>();
builder.Services.AddScoped<ICustomerRepository, CustomerRepository>();
builder.Services.AddScoped<IEncryptionService, EncryptionService>();
builder.Services.AddScoped<IPasswordHasher<Customer>, PasswordHasher<Customer>>();
builder.Services.AddScoped<IAddressService, AddressService>();
builder.Services.AddScoped<IAddressRepository, AddressRepository>();
builder.Services.AddScoped<ICartService, CartService>();
builder.Services.AddScoped<ICartRepository, CartRepository>();
builder.Services.AddScoped<ICartItemRepository, CartItemRepository>();
builder.Services.AddScoped<IUserPointService, UserPointService>();
builder.Services.AddScoped<IUserPointRepository, UserPointRepository>();
builder.Services.AddScoped<ICouponService, CouponService>();
builder.Services.AddScoped<ICouponRepository, CouponRepository>();
builder.Services.AddScoped<IShippingCarrierService, ShippingCarrierService>();
builder.Services.AddScoped<IMailTemplateService, MailTemplateService>();
builder.Services.AddScoped<ICompanyInfoService, CompanyInfoService>();
builder.Services.AddScoped<ICompanyInfoRepository, CompanyInfoRepository>();

// Shipping modülleri
builder.Services.AddInfrastructure();
builder.Services.AddModules();

// Diğer servis kayıtlarından sonra, app oluşturulmadan önce ekleyin
builder.Services.AddScoped(typeof(IGenericRepository<>), typeof(GenericRepository<>));
builder.Services.AddControllers();
var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}

// HTTPS yönlendirmesini geçici olarak devre dışı bırakıyoruz
// app.UseHttpsRedirection();

// CORS middleware'i authentication ve authorization middleware'inden önce olmalı
app.UseCorsExt();
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();
// Veritabanını oluştur ve seed data ekle
if (app.Environment.IsDevelopment())
{
    using (var scope = app.Services.CreateScope())
    {
        var services = scope.ServiceProvider;
        try
        {
            var context = services.GetRequiredService<B2BDbContext>();
            var userManager = services.GetRequiredService<UserManager<B2BUser>>();
            var roleManager = services.GetRequiredService<RoleManager<B2BRole>>();

            // Veritabanını oluştur
            context.Database.Migrate();

            // Seed data ekle
            await SeedData.SeedAllData(context, userManager, roleManager);

            // Shipping carrier seeder
            var logger = services.GetRequiredService<ILogger<ShippingCarrierSeeder>>();
            var shippingSeeder = new ShippingCarrierSeeder(context, logger);
            var paymentLogger = services.GetRequiredService<ILogger<PaymentProviderSeeder>>();
            await shippingSeeder.SeedAsync();
            var paymentProviderSeeder = new PaymentProviderSeeder(context, paymentLogger);
            await paymentProviderSeeder.SeedAsync();
            Console.WriteLine("Veritabanı başarıyla oluşturuldu ve seed data eklendi.");
        }
        catch (Exception ex)
        {
            var logger = services.GetRequiredService<ILogger<Program>>();
            logger.LogError(ex, "Veritabanı oluşturulurken bir hata oluştu.");
        }
    }
}

app.Run();
