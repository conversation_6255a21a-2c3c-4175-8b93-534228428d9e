using MailAPI.Consumers;
using Modules.Mail.Abstraction.Models;
using MailAPI.Services;
using MailAPI.Services.Interfaces;
using MailAPI.Services.Providers;
using MassTransit;

var builder = WebApplication.CreateBuilder(args);

// Konfigürasyon ayarlarını ekle
builder.Services.Configure<SmtpSettings>(builder.Configuration.GetSection("SmtpSettings"));

// Mail servislerini ekle
builder.Services.AddSingleton<IMailProvider, SmtpMailProvider>();
builder.Services.AddSingleton<MailService>();

// RabbitMQ ve MassTransit konfigürasyonu
builder.Services.AddMassTransit(config =>
{
    config.AddConsumer<MailConsumer>();

    config.UsingRabbitMq((context, cfg) =>
    {
        var rabbitMqConfig = builder.Configuration.GetSection("RabbitMQ");

        cfg.Host(rabbitMqConfig["Host"], rabbitMqConfig["VirtualHost"], h =>
        {
            h.Username( rabbitMqConfig["Username"] ?? "guest");
            h.Password(rabbitMqConfig["Password"] ?? "guest");
        });

        // Fanout exchange konfigürasyonu
        cfg.Publish<MailMessage>(e => e.ExchangeType = "fanout");

        cfg.ReceiveEndpoint(rabbitMqConfig["MailQueue"] ?? "mail-queue", e =>
        {
            e.ConfigureConsumer<MailConsumer>(context);
        });
    });
});

// Add services to the container.
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();
builder.WebHost.ConfigureKestrel(options =>
{
    options.ListenAnyIP(33803); // İlgili portu yaz
});

// Logging ekle
builder.Services.AddLogging();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}

app.UseHttpsRedirection();



// Basit bir sağlık kontrolü için endpoint ekle
app.MapGet("/health", () => "Mail servisi çalışıyor!");

// Test mail gönderimi için endpoint
app.MapPost("/send-test-mail", async (MailMessage mailMessage, MailService mailService) =>
{
    var result = await mailService.SendMailAsync(mailMessage);
    return result ? Results.Ok("Mail başarıyla gönderildi") : Results.BadRequest("Mail gönderilemedi");
});

app.Run();
