using MediaAPI.Extensions;
using Microsoft.Extensions.FileProviders;

var builder = WebApplication.CreateBuilder(args);

// Servis yapılandırmaları
builder.Services.AddControllers();
builder.Services.AddSwaggerDocumentation();
builder.Services.AddCorsPolicy(builder.Configuration);
builder.Services.AddApplicationServices(builder.Configuration);
builder.Services.AddMessaging(builder.Configuration);

builder.WebHost.ConfigureKestrel(options =>
{
    options.ListenAnyIP(33802); // MediaAPI portu
});

// ImageProcessor sınıfının gerçek implementasyonunu kaydet
// builder.Services.AddSingleton<SharedKernel.SharedKernel.Models.Media.ImageProcessor, ImageProcessorService>();

var app = builder.Build();

// HTTP request pipeline yapılandırması
if (app.Environment.IsDevelopment())
{
    app.UseSwaggerDocumentation();
}

// Global exception handler
app.UseGlobalExceptionHandler(app.Services.GetRequiredService<ILoggerFactory>());

app.UseHttpsRedirection();
app.UseCors("CorsPolicy");

// Ürün ve kampanya görselleri için wwwroot klasörünü oluştur
var imagesPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "images");
Directory.CreateDirectory(imagesPath);

// Ürün görselleri
Directory.CreateDirectory(Path.Combine(imagesPath, "products"));
Directory.CreateDirectory(Path.Combine(imagesPath, "products", "original"));
Directory.CreateDirectory(Path.Combine(imagesPath, "products", "webp"));
Directory.CreateDirectory(Path.Combine(imagesPath, "products", "thumbnail"));
Directory.CreateDirectory(Path.Combine(imagesPath, "products", "thumbnail", "sm"));
Directory.CreateDirectory(Path.Combine(imagesPath, "products", "thumbnail", "md"));

// Kampanya görselleri
Directory.CreateDirectory(Path.Combine(imagesPath, "campaigns"));
Directory.CreateDirectory(Path.Combine(imagesPath, "campaigns", "original"));
Directory.CreateDirectory(Path.Combine(imagesPath, "campaigns", "webp"));
Directory.CreateDirectory(Path.Combine(imagesPath, "campaigns", "thumbnail"));
Directory.CreateDirectory(Path.Combine(imagesPath, "campaigns", "thumbnail", "sm"));
Directory.CreateDirectory(Path.Combine(imagesPath, "campaigns", "thumbnail", "md"));

// Statik dosya sunumu yapılandır - tüm ortamlarda çalışacak şekilde
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(Path.Combine(Directory.GetCurrentDirectory(), "wwwroot")),
    RequestPath = ""
});

app.MapControllers();

app.Run();
