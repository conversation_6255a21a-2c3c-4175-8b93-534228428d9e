using System.Text.Json;
using System.Text.RegularExpressions;
using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Core.Entities;
using Core.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Infrastructure.Services;

/// <summary>
/// Mail template servisi implementasyonu
/// </summary>
public class MailTemplateService : IMailTemplateService
{
    private readonly IGenericRepository<MailTemplate> _templateRepository;
    private readonly ILogger<MailTemplateService> _logger;

    public MailTemplateService(
        IGenericRepository<MailTemplate> templateRepository,
        ILogger<MailTemplateService> logger)
    {
        _templateRepository = templateRepository;
        _logger = logger;
    }

    public async Task<IEnumerable<MailTemplateDto>> GetAllAsync()
    {
        try
        {
            var templates = await _templateRepository.Query()
                .Where(t => !t.IsDeleted)
                .OrderBy(t => t.Category)
                .ThenBy(t => t.SortOrder)
                .ThenBy(t => t.Name)
                .ToListAsync();

            return templates.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all mail templates");
            throw;
        }
    }

    public async Task<MailTemplateDto?> GetByIdAsync(Guid id)
    {
        try
        {
            var template = await _templateRepository.Query()
                .FirstOrDefaultAsync(t => t.Id == id && !t.IsDeleted);

            return template != null ? MapToDto(template) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting mail template by ID: {Id}", id);
            throw;
        }
    }

    public async Task<MailTemplateDto?> GetByShortCodeAsync(string shortCode)
    {
        try
        {
            var template = await _templateRepository.Query()
                .FirstOrDefaultAsync(t => t.ShortCode == shortCode && !t.IsDeleted);

            return template != null ? MapToDto(template) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting mail template by short code: {ShortCode}", shortCode);
            throw;
        }
    }

    public async Task<IEnumerable<MailTemplateDto>> GetByCategoryAsync(string category)
    {
        try
        {
            var templates = await _templateRepository.Query()
                .Where(t => !t.IsDeleted && t.Category == category)
                .OrderBy(t => t.SortOrder)
                .ThenBy(t => t.Name)
                .ToListAsync();

            return templates.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting mail templates by category: {Category}", category);
            throw;
        }
    }

    public async Task<IEnumerable<MailTemplateDto>> GetActiveTemplatesAsync()
    {
        try
        {
            var templates = await _templateRepository.Query()
                .Where(t => !t.IsDeleted && t.IsActive)
                .OrderBy(t => t.Category)
                .ThenBy(t => t.SortOrder)
                .ThenBy(t => t.Name)
                .ToListAsync();

            return templates.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active mail templates");
            throw;
        }
    }

    public async Task<MailTemplateDto> CreateAsync(CreateMailTemplateDto dto)
    {
        try
        {
            // ShortCode benzersizlik kontrolü
            var existingTemplate = await _templateRepository.Query()
                .FirstOrDefaultAsync(t => t.ShortCode == dto.ShortCode && !t.IsDeleted);

            if (existingTemplate != null)
            {
                throw new ArgumentException($"Mail template with short code '{dto.ShortCode}' already exists");
            }

            var template = new MailTemplate
            {
                Id = Guid.CreateVersion7(),
                ShortCode = dto.ShortCode,
                Name = dto.Name,
                Description = dto.Description,
                Subject = dto.Subject,
                Content = dto.Content,
                Variables = dto.Variables != null ? JsonSerializer.Serialize(dto.Variables) : null,
                DefaultFromEmail = dto.DefaultFromEmail,
                DefaultFromName = dto.DefaultFromName,
                Category = dto.Category,
                IsActive = dto.IsActive,
                IsSystem = false, // Kullanıcı oluşturduğu için sistem template'i değil
                SortOrder = dto.SortOrder,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            await _templateRepository.AddAsync(template);
            await _templateRepository.SaveChangesAsync();

            _logger.LogInformation("Mail template created: {Name} ({ShortCode})", template.Name, template.ShortCode);

            return MapToDto(template);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating mail template: {Name}", dto.Name);
            throw;
        }
    }

    public async Task<MailTemplateDto> UpdateAsync(Guid id, UpdateMailTemplateDto dto)
    {
        try
        {
            var template = await _templateRepository.GetByIdAsync(id);
            if (template == null || template.IsDeleted)
            {
                throw new ArgumentException("Mail template not found");
            }

            // Sistem template'i güncelleme kontrolü
            if (template.IsSystem)
            {
                throw new ArgumentException("System templates cannot be updated");
            }

            // Güncelleme
            if (!string.IsNullOrEmpty(dto.Name))
                template.Name = dto.Name;
            
            if (dto.Description != null)
                template.Description = dto.Description;
            
            if (!string.IsNullOrEmpty(dto.Subject))
                template.Subject = dto.Subject;
            
            if (!string.IsNullOrEmpty(dto.Content))
                template.Content = dto.Content;
            
            if (dto.Variables != null)
                template.Variables = JsonSerializer.Serialize(dto.Variables);
            
            if (dto.DefaultFromEmail != null)
                template.DefaultFromEmail = dto.DefaultFromEmail;
            
            if (dto.DefaultFromName != null)
                template.DefaultFromName = dto.DefaultFromName;
            
            if (dto.Category != null)
                template.Category = dto.Category;
            
            if (dto.IsActive.HasValue)
                template.IsActive = dto.IsActive.Value;
            
            if (dto.SortOrder.HasValue)
                template.SortOrder = dto.SortOrder.Value;

            template.UpdatedAt = DateTime.UtcNow;

            _templateRepository.Update(template);
            await _templateRepository.SaveChangesAsync();

            _logger.LogInformation("Mail template updated: {Name} ({ShortCode})", template.Name, template.ShortCode);

            return MapToDto(template);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating mail template: {Id}", id);
            throw;
        }
    }

    public async Task<bool> DeleteAsync(Guid id)
    {
        try
        {
            var template = await _templateRepository.GetByIdAsync(id);
            if (template == null || template.IsDeleted)
            {
                return false;
            }

            // Sistem template'i silme kontrolü
            if (template.IsSystem)
            {
                throw new ArgumentException("System templates cannot be deleted");
            }

            template.IsDeleted = true;
            template.UpdatedAt = DateTime.UtcNow;

            _templateRepository.Update(template);
            await _templateRepository.SaveChangesAsync();

            _logger.LogInformation("Mail template deleted: {Name} ({ShortCode})", template.Name, template.ShortCode);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting mail template: {Id}", id);
            throw;
        }
    }

    public async Task<bool> ToggleActiveAsync(Guid id)
    {
        try
        {
            var template = await _templateRepository.GetByIdAsync(id);
            if (template == null || template.IsDeleted)
            {
                return false;
            }

            template.IsActive = !template.IsActive;
            template.UpdatedAt = DateTime.UtcNow;

            _templateRepository.Update(template);
            await _templateRepository.SaveChangesAsync();

            _logger.LogInformation("Mail template active status toggled: {Name} ({ShortCode}) - New status: {IsActive}", 
                template.Name, template.ShortCode, template.IsActive);

            return template.IsActive;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling mail template active status: {Id}", id);
            throw;
        }
    }

    public async Task<(string subject, string content)> RenderTemplateAsync(string shortCode, Dictionary<string, string> variables)
    {
        try
        {
            var template = await GetByShortCodeAsync(shortCode);
            if (template == null)
            {
                throw new ArgumentException($"Mail template not found: {shortCode}");
            }

            if (!template.IsActive)
            {
                throw new ArgumentException($"Mail template is not active: {shortCode}");
            }

            // Template değişkenlerini değiştir
            var renderedSubject = ReplaceVariables(template.Subject, variables);
            var renderedContent = ReplaceVariables(template.Content, variables);

            return (renderedSubject, renderedContent);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rendering mail template: {ShortCode}", shortCode);
            throw;
        }
    }

    public async Task<List<string>?> GetTemplateVariablesAsync(string shortCode)
    {
        try
        {
            var template = await GetByShortCodeAsync(shortCode);
            return template?.Variables;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting template variables: {ShortCode}", shortCode);
            throw;
        }
    }

    private static string ReplaceVariables(string content, Dictionary<string, string> variables)
    {
        if (string.IsNullOrEmpty(content) || variables == null || !variables.Any())
            return content;

        var result = content;
        
        // {{variableName}} formatındaki değişkenleri değiştir
        foreach (var variable in variables)
        {
            var pattern = $@"\{{\{{\s*{Regex.Escape(variable.Key)}\s*\}}\}}";
            result = Regex.Replace(result, pattern, variable.Value, RegexOptions.IgnoreCase);
        }

        return result;
    }

    private static MailTemplateDto MapToDto(MailTemplate template)
    {
        var variables = string.IsNullOrEmpty(template.Variables) 
            ? new List<string>() 
            : JsonSerializer.Deserialize<List<string>>(template.Variables) ?? new List<string>();

        return new MailTemplateDto
        {
            Id = template.Id,
            ShortCode = template.ShortCode,
            Name = template.Name,
            Description = template.Description,
            Subject = template.Subject,
            Content = template.Content,
            Variables = variables,
            DefaultFromEmail = template.DefaultFromEmail,
            DefaultFromName = template.DefaultFromName,
            Category = template.Category,
            IsActive = template.IsActive,
            IsSystem = template.IsSystem,
            SortOrder = template.SortOrder,
            CreatedAt = template.CreatedAt,
            UpdatedAt = template.UpdatedAt
        };
    }
}
