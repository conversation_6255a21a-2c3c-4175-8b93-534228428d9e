using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Core.Entities;
using Core.Enums;
using Core.Interfaces;
using Microsoft.EntityFrameworkCore;
using Infrastructure.Extensions;

namespace Infrastructure.Services;

public class OrderService : IOrderService
{
    private readonly IGenericRepository<Order> _orderRepository;
    private readonly IGenericRepository<OrderRow> _orderRowRepository;
    private readonly IGenericRepository<Customer> _customerRepository;
    private readonly IGenericRepository<Product> _productRepository;

    public OrderService(
        IGenericRepository<Order> orderRepository,
        IGenericRepository<OrderRow> orderRowRepository,
        IGenericRepository<Customer> customerRepository,
        IGenericRepository<Product> productRepository)
    {
        _orderRepository = orderRepository;
        _orderRowRepository = orderRowRepository;
        _customerRepository = customerRepository;
        _productRepository = productRepository;
    }

    public async Task<List<OrderListDto>> GetListAsync(int? page = null, int? pageSize = null)
    {
        var query = _orderRepository.Query()
            .Include(o => o.Customer)
            .Include(o => o.OrderRows)
            .Where(o => !o.IsDeleted)
            .OrderByDescending(o => o.CreatedAt);

        if (page.HasValue && pageSize.HasValue)
        {
            query = (IOrderedQueryable<Order>)query.Skip((page.Value - 1) * pageSize.Value).Take(pageSize.Value);
        }

        var orders = await query.ToListAsync();

        return orders.Select(o => new OrderListDto
        {
            Id = o.Id,
            OrderNumber = o.OrderNumber,
            Status = o.Status,
            TotalAmount = o.TotalAmount,
            CreatedAt = o.CreatedAt,
            CustomerName = o.Customer.NameSurname.Decrypt(),
            ItemCount = o.OrderRows.Count
        }).ToList();
    }

    public async Task<OrderDto?> GetByIdAsync(Guid id)
    {
        var order = await _orderRepository.Query()
            .Include(o => o.Customer)
            .Include(o => o.Address)
            .Include(o => o.OrderRows)
                .ThenInclude(or => or.Product)
                .ThenInclude(p=> p.Images)
            .Include(o => o.Payments)
            .Include(o => o.Shipments)
            .FirstOrDefaultAsync(o => o.Id == id && !o.IsDeleted);

        if (order == null) return null;

        return new OrderDto
        {
            Id = order.Id,
            CustomerId = order.CustomerId,
            AddressId = order.AddressId,
            OrderNumber = order.OrderNumber,
            Status = order.Status,
            TotalAmount = order.TotalAmount,
            DiscountAmount = order.DiscountAmount,
            ShippingAmount = order.ShippingAmount,
            TaxAmount = order.TaxAmount,
            Notes = order.Notes,
            CreatedAt = order.CreatedAt,
            UpdatedAt = order.UpdatedAt,
            Customer = order.Customer != null ? new CustomerDto
            {
                Id = order.Customer.Id,
                NameSurname = order.Customer.NameSurname.Decrypt(),
                PhoneNumber = order.Customer.PhoneNumber.DecryptIfNotEmpty(),
                Email = order.Customer.Email.DecryptIfNotEmpty(),
                TaxOrIdentityNumber = order.Customer.TaxOrIdentityNumber.DecryptIfNotEmpty(),
                TaxOffice = order.Customer.TaxOffice
            } : null,
            Address = order.Address != null ? new AddressDto
            {
                Id = order.Address.Id,
                Name = order.Address.Name,
                Line1 = order.Address.Line1,
                Line2 = order.Address.Line2,
                City = order.Address.City,
                District = order.Address.District,
                Country = order.Address.Country,
                PostalCode = order.Address.PostalCode,
                AddressType = order.Address.AddressType,
                IsDefault = order.Address.IsDefault,
                CustomerId = order.Address.CustomerId,
                DealerId = order.Address.DealerId,
                CreatedAt = order.Address.CreatedAt,
                UpdatedAt = order.Address.UpdatedAt
            } : null,
            OrderRows = order.OrderRows.Select(or => new OrderRowDto
            {
                Id = or.Id,
                OrderId = or.OrderId,
                ProductId = or.ProductId,
                Quantity = or.Quantity,
                Price = or.Price,
                DiscountedPrice = or.DiscountedPrice,
                Product = or.Product != null ? new ProductDto
                {
                    Id = or.Product.Id,
                    Name = or.Product.Name,
                    Description = or.Product.Description,
                    Sku = or.Product.Sku,
                    Price = or.Product.Price,
                    StockQuantity = or.Product.StockQuantity,
                    Images = or.Product.Images.Where(i=>i.IsMainImage).Select(i => new ProductImageDto
                    {
                        Id = i.Id,
                        ProductId = i.ProductId,
                        OriginalImagePath = i.OriginalPath,
                        ThumbnailSmallPath = i.ThumbnailSmPath,
                        ThumbnailMediumPath = i.ThumbnailMdPath,
                        FileName = i.Name,
                        AltText = i.AltText,
                        OriginalWidth = i.OriginalWidth,
                        OriginalHeight = i.OriginalHeight,
                        FileSizeBytes = i.FileSizeBytes,
                        IsMainImage = i.IsMainImage,
                        SortOrder = i.SortOrder
                    }).ToList()
                } : null
            }).ToList(),
            Payments = order.Payments.Select(p => new PaymentDto
            {
                Id = p.Id,
                OrderId = p.OrderId,
                Amount = p.Amount,
                Description = p.Description,
                PaymentMethod = p.PaymentMethod,
                Status = p.Status,
                MaskedCardNumber = p.MaskedCardNumber,
                PaymentResponse = p.PaymentResponse,
                PaymentResponseCode = p.PaymentResponseCode,
                PaymentResponseMessage = p.PaymentResponseMessage,
                PaymentResponseTransactionId = p.PaymentResponseTransactionId,
                CreatedAt = p.CreatedAt
            }).ToList(),
            Shipments = order.Shipments.Select(s => new ShipmentDto
            {
                Id = s.Id,
                OrderId = s.OrderId,
                TrackingNumber = s.TrackingNumber,
                Carrier = s.Carrier,
                Status = s.Status,
                ShippedAt = s.ShippedAt,
                DeliveredAt = s.DeliveredAt,
                Notes = s.Notes,
                CreatedAt = s.CreatedAt
            }).ToList()
        };
    }

    public async Task<OrderDto?> GetByOrderNumberAsync(string orderNumber)
    {
        var order = await _orderRepository.Query()
            .Include(o => o.Customer)
            .Include(o => o.Address)
            .Include(o => o.OrderRows)
                .ThenInclude(or => or.Product)
            .Include(o => o.Payments)
            .Include(o => o.Shipments)
            .FirstOrDefaultAsync(o => o.OrderNumber == orderNumber && !o.IsDeleted);

        if (order == null) return null;

        return await GetByIdAsync(order.Id);
    }

    public async Task<Guid> CreateAsync(OrderCreateDto dto)
    {
        // Generate order number
        var orderNumber = await GenerateOrderNumberAsync();

        var totalAmount = dto.OrderRows.Sum(or => or.Price * or.Quantity)
                         + dto.ShippingAmount - dto.DiscountAmount;
        // Calculate total amount
        var totalDiscountedAmount = dto.OrderRows.Sum(or => or.DiscountedPrice * or.Quantity)
                         + dto.ShippingAmount - dto.DiscountAmount;

        var order = new Order
        {
            Id = Guid.CreateVersion7(),
            CustomerId = dto.CustomerId,
            AddressId = dto.AddressId,
            OrderNumber = orderNumber,
            Status = OrderStatus.Pending,
            TotalAmount = totalAmount,
            DiscountAmount = totalAmount - totalDiscountedAmount + dto.DiscountAmount,
            ShippingAmount = dto.ShippingAmount,
            TaxAmount = totalDiscountedAmount - totalDiscountedAmount / 1.2m,
            Notes = dto.Notes,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await _orderRepository.AddAsync(order);

        // Add order rows
        foreach (var rowDto in dto.OrderRows)
        {
            var orderRow = new OrderRow
            {
                Id = Guid.CreateVersion7(),
                OrderId = order.Id,
                ProductId = rowDto.ProductId,
                Quantity = rowDto.Quantity,
                Price = rowDto.Price,
                DiscountedPrice = rowDto.DiscountedPrice,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            await _orderRowRepository.AddAsync(orderRow);
        }

        return order.Id;
    }

    public async Task UpdateAsync(OrderUpdateDto dto)
    {
        var order = await _orderRepository.GetByIdAsync(dto.Id);
        if (order == null || order.IsDeleted)
            throw new ArgumentException("Order not found");

        order.Status = dto.Status;
        order.DiscountAmount = dto.DiscountAmount;
        order.ShippingAmount = dto.ShippingAmount;
        order.TaxAmount = dto.TaxAmount;
        order.Notes = dto.Notes;
        order.UpdatedAt = DateTime.UtcNow;

        // Recalculate total amount
        var orderRows = await _orderRowRepository.Query()
            .Where(or => or.OrderId == order.Id && !or.IsDeleted)
            .ToListAsync();

        order.TotalAmount = orderRows.Sum(or => or.DiscountedPrice * or.Quantity)
                           + order.ShippingAmount + order.TaxAmount - order.DiscountAmount;

        _orderRepository.Update(order);
        await _orderRepository.SaveChangesAsync();
    }

    public async Task UpdateStatusAsync(OrderStatusUpdateDto dto)
    {
        var order = await _orderRepository.GetByIdAsync(dto.Id);
        if (order == null || order.IsDeleted)
            throw new ArgumentException("Order not found");

        order.Status = dto.Status;
        if (!string.IsNullOrEmpty(dto.Notes))
            order.Notes = dto.Notes;
        order.UpdatedAt = DateTime.UtcNow;

        _orderRepository.Update(order);
        await _orderRepository.SaveChangesAsync();
    }

    public async Task DeleteAsync(Guid id)
    {
        var order = await _orderRepository.GetByIdAsync(id);
        if (order == null || order.IsDeleted)
            throw new ArgumentException("Order not found");

        order.IsDeleted = true;
        order.UpdatedAt = DateTime.UtcNow;

        _orderRepository.Update(order);
        await _orderRepository.SaveChangesAsync();
    }

    public async Task<List<OrderListDto>> GetOrdersByCustomerAsync(Guid customerId)
    {
        var orders = await _orderRepository.Query()
            .Include(o => o.Customer)
            .Include(o => o.OrderRows)
            .Where(o => o.CustomerId == customerId && !o.IsDeleted)
            .OrderByDescending(o => o.CreatedAt)
            .ToListAsync();

        return orders.Select(o => new OrderListDto
        {
            Id = o.Id,
            OrderNumber = o.OrderNumber,
            Status = o.Status,
            TotalAmount = o.TotalAmount,
            CreatedAt = o.CreatedAt,
            CustomerName = o.Customer.NameSurname,
            ItemCount = o.OrderRows.Count
        }).ToList();
    }

    public async Task<List<OrderListDto>> GetOrdersByStatusAsync(OrderStatus status)
    {
        var orders = await _orderRepository.Query()
            .Include(o => o.Customer)
            .Include(o => o.OrderRows)
            .Where(o => o.Status == status && !o.IsDeleted)
            .OrderByDescending(o => o.CreatedAt)
            .ToListAsync();

        return orders.Select(o => new OrderListDto
        {
            Id = o.Id,
            OrderNumber = o.OrderNumber,
            Status = o.Status,
            TotalAmount = o.TotalAmount,
            CreatedAt = o.CreatedAt,
            CustomerName = o.Customer.NameSurname,
            ItemCount = o.OrderRows.Count
        }).ToList();
    }

    public async Task<List<OrderListDto>> SearchOrdersAsync(string searchTerm)
    {
        var orders = await _orderRepository.Query()
            .Include(o => o.Customer)
            .Include(o => o.OrderRows)
            .Where(o => !o.IsDeleted &&
                       (o.OrderNumber.Contains(searchTerm) ||
                        o.Customer.NameSurname.Contains(searchTerm) ||
                        o.Customer.Email!.Contains(searchTerm)))
            .OrderByDescending(o => o.CreatedAt)
            .ToListAsync();

        return orders.Select(o => new OrderListDto
        {
            Id = o.Id,
            OrderNumber = o.OrderNumber,
            Status = o.Status,
            TotalAmount = o.TotalAmount,
            CreatedAt = o.CreatedAt,
            CustomerName = o.Customer.NameSurname,
            ItemCount = o.OrderRows.Count
        }).ToList();
    }

    public async Task<decimal> GetTotalSalesAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = _orderRepository.Query()
            .Where(o => !o.IsDeleted && o.Status != OrderStatus.Cancelled);

        if (startDate.HasValue)
            query = query.Where(o => o.CreatedAt >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(o => o.CreatedAt <= endDate.Value);

        return await query.SumAsync(o => o.TotalAmount);
    }

    public async Task<int> GetOrderCountAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = _orderRepository.Query()
            .Where(o => !o.IsDeleted);

        if (startDate.HasValue)
            query = query.Where(o => o.CreatedAt >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(o => o.CreatedAt <= endDate.Value);

        return await query.CountAsync();
    }

    private async Task<string> GenerateOrderNumberAsync()
    {
        var today = DateTime.UtcNow;
        var prefix = $"ORD{today:yyyyMMdd}";

        var lastOrder = await _orderRepository.Query()
            .Where(o => o.OrderNumber.StartsWith(prefix))
            .OrderByDescending(o => o.OrderNumber)
            .FirstOrDefaultAsync();

        if (lastOrder == null)
        {
            return $"{prefix}001";
        }

        var lastNumber = lastOrder.OrderNumber.Substring(prefix.Length);
        if (int.TryParse(lastNumber, out var number))
        {
            return $"{prefix}{(number + 1):D3}";
        }

        return $"{prefix}001";
    }
}
