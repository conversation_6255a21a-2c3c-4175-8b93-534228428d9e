using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Application.Contracts.Services;
using Core.Entities;
using Core.Enums;
using Core.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Services;

/// <summary>
/// B2C müşteri ürün servisi implementasyonu - Server-side rendering ve SEO odaklı
/// </summary>
public class CustomerProductService : ICustomerProductService
{
    private readonly IGenericRepository<Product> _productRepository;
    private readonly IGenericRepository<ProductCategory> _categoryRepository;
    private readonly IGenericRepository<ProductBrand> _brandRepository;
    private readonly IGenericRepository<ProductImage> _imageRepository;
    private readonly IGenericRepository<ProductSeo> _seoRepository;
    private readonly IGenericRepository<ProductReview> _reviewRepository;
    private readonly IGenericRepository<ProductAttribute> _attributeRepository;
    private readonly IGenericRepository<ProductAttributeMapping> _attributeMappingRepository;
    private readonly IProductVolumeService _productVolumeService;
    private readonly ICampaignCalculationService _campaignCalculationService;
    private readonly ICampaignService _campaignService;

    public CustomerProductService(
        IGenericRepository<Product> productRepository,
        IGenericRepository<ProductCategory> categoryRepository,
        IGenericRepository<ProductBrand> brandRepository,
        IGenericRepository<ProductImage> imageRepository,
        IGenericRepository<ProductSeo> seoRepository,
        IGenericRepository<ProductReview> reviewRepository,
        IGenericRepository<ProductAttribute> attributeRepository,
        IGenericRepository<ProductAttributeMapping> attributeMappingRepository,
        IProductVolumeService productVolumeService,
        ICampaignCalculationService campaignCalculationService,
        ICampaignService campaignService)
    {
        _productRepository = productRepository;
        _categoryRepository = categoryRepository;
        _brandRepository = brandRepository;
        _imageRepository = imageRepository;
        _seoRepository = seoRepository;
        _reviewRepository = reviewRepository;
        _attributeRepository = attributeRepository;
        _attributeMappingRepository = attributeMappingRepository;
        _productVolumeService = productVolumeService;
        _campaignCalculationService = campaignCalculationService;
        _campaignService = campaignService;
    }

    public async Task<ProductPageDataDto> GetProductPageDataAsync(ProductFilterDto filter)
    {
        var query = BuildProductQuery(filter);

        // Apply pagination
        var totalCount = await query.CountAsync();
        var products = await query
            .Skip((filter.Page - 1) * filter.PageSize)
            .Take(filter.PageSize)
            .ToListAsync();

        var productDtos = products.Select(MapToCustomerListProductDto).ToList();

        var filterOptions = await GetFilterOptionsAsync(filter.PageType, filter.CategorySlug);
        var pagination = CreatePaginationDto(filter.Page, filter.PageSize, totalCount);
        var metadata = await GetPageMetadataAsync(new PageMetadataRequestDto
        {
            PageType = filter.PageType ?? "all",
            CategorySlug = filter.CategorySlug
        });

        return new ProductPageDataDto
        {
            Products = productDtos,
            FilterOptions = filterOptions,
            Pagination = pagination,
            Metadata = metadata,
            PageType = filter.PageType ?? "all",
            CategorySlug = filter.CategorySlug,
            TotalProducts = totalCount
        };
    }

    public async Task<CustomerProductListDto> GetProductListAsync(ProductFilterDto filter)
    {
        var query = BuildProductQuery(filter);

        var totalCount = await query.CountAsync();
        var products = await query
            .Skip((filter.Page - 1) * filter.PageSize)
            .Take(filter.PageSize)
            .ToListAsync();

        var productDtos = products.Select(MapToCustomerListProductDto).ToList();
        var filterOptions = await GetFilterOptionsAsync(filter.PageType, filter.CategorySlug);
        var pagination = CreatePaginationDto(filter.Page, filter.PageSize, totalCount);

        return new CustomerProductListDto
        {
            Products = productDtos,
            Filters = MapToCustomerFiltersDto(filterOptions),
            Pagination = CreateCustomerPaginationDto(filter.Page, filter.PageSize, totalCount)
        };
    }

    public async Task<CustomerProductDto?> GetProductBySlugAsync(string slug)
    {
        var product = await _productRepository.Query()
            .Include(p => p.Category)
            .Include(p => p.Brand)
            .Include(p => p.Images)
            .Include(p => p.Reviews)
                .ThenInclude(r => r.Customer)
            .Include(p => p.Seo)
            .Include(p => p.AttributeMappings)
                .ThenInclude(am => am.Attribute)
            .Include(p => p.AttributeMappings)
                .ThenInclude(am => am.AttributeValue)
            .Include(p => p.Variants)
                .ThenInclude(v => v.AttributeMappings)
                    .ThenInclude(am => am.Attribute)
            .Include(p => p.Variants)
                .ThenInclude(v => v.AttributeMappings)
                    .ThenInclude(am => am.AttributeValue)
            .FirstOrDefaultAsync(p => p.Slug == slug && p.IsActive && !p.IsDeleted);

        return product != null ? await MapToCustomerProductDtoAsync(product) : null;
    }

    public async Task<ProductDetailPageDataDto?> GetProductDetailPageDataAsync(string slug)
    {
        var product = await GetProductBySlugAsync(slug);
        if (product == null) return null;

        var recommendedProducts = await GetRecommendedProductsAsync(8);
        var relatedProducts = await GetRelatedProductsAsync(product.Id, 8);
        var metadata = await GetProductMetadataAsync(slug);

        return new ProductDetailPageDataDto
        {
            Product = product,
            RecommendedProducts = recommendedProducts,
            RelatedProducts = relatedProducts,
            Metadata = metadata
        };
    }

    public async Task<List<CustomerProductReviewDto>> GetProductReviewsAsync(Guid productId, int page = 1, int pageSize = 10)
    {
        var reviews = await _reviewRepository.Query()
            .Include(r => r.Customer)
            .Where(r => r.ProductId == productId && r.IsActive && !r.IsDeleted)
            .OrderByDescending(r => r.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return reviews.Select(MapToCustomerProductReviewDto).ToList();
    }

    public async Task<int> GetProductReviewCountAsync(Guid productId)
    {
        return await _reviewRepository.CountAsync(r => r.ProductId == productId && r.IsActive && !r.IsDeleted);
    }

    public async Task<decimal> GetProductAverageRatingAsync(Guid productId)
    {
        var reviews = await _reviewRepository.Query()
            .Where(r => r.ProductId == productId && r.IsActive && !r.IsDeleted)
            .ToListAsync();

        return reviews.Any() ? (decimal)reviews.Average(r => r.Rating) : 0;
    }

    public async Task<List<CustomerListProductDto>> GetFeaturedProductsAsync(int count = 12)
    {
        var products = await _productRepository.Query()
            .Include(p => p.Category)
            .Include(p => p.Brand)
            .Include(p => p.Images)
            .Where(p => p.IsActive && !p.IsDeleted && p.ParentProductId == null)
            .OrderByDescending(p => p.CreatedAt)
            .Take(count)
            .ToListAsync();

        return products.Select(MapToCustomerListProductDto).ToList();
    }

    public async Task<List<CustomerListProductDto>> GetRecommendedProductsAsync(int count = 12)
    {
        // For now, return random products. In future, implement recommendation algorithm
        var products = await _productRepository.Query()
            .Include(p => p.Category)
            .Include(p => p.Brand)
            .Include(p => p.Images)
            .Where(p => p.IsActive && !p.IsDeleted && p.ParentProductId == null)
            .OrderBy(p => Guid.NewGuid()) // Random order for now
            .Take(count)
            .ToListAsync();

        return products.Select(MapToCustomerListProductDto).ToList();
    }

    public async Task<List<CustomerListProductDto>> GetNewSeasonProductsAsync(int count = 12)
    {
        // Products created in the last 3 months
        var threeMonthsAgo = DateTime.UtcNow.AddMonths(-3);

        var products = await _productRepository.Query()
            .Include(p => p.Category)
            .Include(p => p.Brand)
            .Include(p => p.Images)
            .Where(p => p.IsActive && !p.IsDeleted && p.ParentProductId == null && p.CreatedAt >= threeMonthsAgo)
            .OrderByDescending(p => p.CreatedAt)
            .Take(count)
            .ToListAsync();

        return products.Select(MapToCustomerListProductDto).ToList();
    }

    public async Task<List<CustomerListProductDto>> GetDiscountedProductsAsync(int count = 12)
    {
        // For now, return products with price > 0. In future, implement discount logic
        var products = await _productRepository.Query()
            .Include(p => p.Category)
            .Include(p => p.Brand)
            .Include(p => p.Images)
            .Where(p => p.IsActive && !p.IsDeleted && p.ParentProductId == null && p.Price.HasValue && p.Price > 0)
            .OrderByDescending(p => p.UpdatedAt)
            .Take(count)
            .ToListAsync();

        return products.Select(MapToCustomerListProductDto).ToList();
    }

    public async Task<List<CustomerListProductDto>> GetRelatedProductsAsync(Guid productId, int count = 8)
    {
        var product = await _productRepository.GetByIdAsync(productId);
        if (product?.CategoryId == null) return [];

        var relatedProducts = await _productRepository.Query()
            .Include(p => p.Category)
            .Include(p => p.Brand)
            .Include(p => p.Images)
            .Where(p => p.IsActive && !p.IsDeleted && p.ParentProductId == null
                       && p.CategoryId == product.CategoryId && p.Id != productId)
            .OrderBy(p => Guid.NewGuid())
            .Take(count)
            .ToListAsync();

        return relatedProducts.Select(MapToCustomerListProductDto).ToList();
    }

    // Helper methods will be added in the next part due to file size limit
    private IQueryable<Product> BuildProductQuery(ProductFilterDto filter)
    {
        var query = _productRepository.Query()
            .Include(p => p.Category)
            .Include(p => p.Brand)
            .Include(p => p.Images)
            .Where(p => p.IsActive && !p.IsDeleted && p.ParentProductId == null);

        // Apply filters
        if (!string.IsNullOrEmpty(filter.CategorySlug))
        {
            query = query.Where(p => p.Category != null && p.Category.Slug == filter.CategorySlug);
        }

        if (filter.CategoryIds?.Any() == true)
        {
            var categoryGuids = filter.CategoryIds.Select(id => new Guid(id.ToString().PadLeft(32, '0'))).ToList();
            query = query.Where(p => p.CategoryId.HasValue && categoryGuids.Contains(p.CategoryId.Value));
        }

        if (filter.BrandIds?.Any() == true)
        {
            var brandGuids = filter.BrandIds.Select(id => new Guid(id.ToString().PadLeft(32, '0'))).ToList();
            query = query.Where(p => p.BrandId.HasValue && brandGuids.Contains(p.BrandId.Value));
        }

        if (filter.PriceMin.HasValue)
        {
            query = query.Where(p => p.Price >= filter.PriceMin);
        }

        if (filter.PriceMax.HasValue)
        {
            query = query.Where(p => p.Price <= filter.PriceMax);
        }

        if (filter.InStock.HasValue)
        {
            if (filter.InStock.Value)
            {
                query = query.Where(p => p.StockQuantity > 0);
            }
            else
            {
                query = query.Where(p => p.StockQuantity <= 0);
            }
        }

        if (!string.IsNullOrEmpty(filter.SearchTerm))
        {
            query = query.Where(p => p.Name.Contains(filter.SearchTerm) ||
                                   (p.Description != null && p.Description.Contains(filter.SearchTerm)));
        }

        // Apply variant attribute filtering
        if (filter.VariantAttributes?.Any() == true)
        {
            foreach (var attributeFilter in filter.VariantAttributes)
            {
                var attributeName = attributeFilter.Key;
                var selectedValues = attributeFilter.Value;

                if (selectedValues?.Any() == true)
                {
                    // Filter products that have variants with the selected attribute values
                    query = query.Where(p => p.Variants.Any(v =>
                        v.AttributeMappings.Any(am =>
                            am.Attribute.ShortName == attributeName &&
                            selectedValues.Contains(am.AttributeValue.Value) &&
                            v.IsActive && !v.IsDeleted)));
                }
            }
        }

        // Apply sorting
        query = filter.SortBy?.ToLower() switch
        {
            "price" => filter.SortDirection?.ToLower() == "desc"
                ? query.OrderByDescending(p => p.Price)
                : query.OrderBy(p => p.Price),
            "created" => filter.SortDirection?.ToLower() == "desc"
                ? query.OrderByDescending(p => p.CreatedAt)
                : query.OrderBy(p => p.CreatedAt),
            _ => filter.SortDirection?.ToLower() == "desc"
                ? query.OrderByDescending(p => p.Name)
                : query.OrderBy(p => p.Name)
        };

        return query;
    }

    // Mapping helper methods
    private CustomerListProductDto MapToCustomerListProductDto(Product product)
    {
        var mainImage = product.Images.FirstOrDefault(i => i.IsMainImage) ?? product.Images.FirstOrDefault();

        return new CustomerListProductDto
        {
            Id = product.Id, // Convert Guid to int for frontend compatibility
            Title = product.Name,
            Slug = product.Slug,
            Brand = product.Brand?.Name,
            Price = product.DiscountedPrice ?? product.Price ?? 0,
            OldPrice = product.DiscountedPrice != null ? product.Price : null,
            ImgSrc = mainImage?.OriginalPath,
            InStock = product.StockQuantity > 0,
            Description = product.Description,
            Category = product.Category?.Name
        };
    }

    private async Task<CustomerProductDto> MapToCustomerProductDtoAsync(Product product)
    {
        var mainImage = product.Images.FirstOrDefault(i => i.IsMainImage) ?? product.Images.FirstOrDefault();
        var reviewCount = product.Reviews.Count(r => r.IsActive && !r.IsDeleted);
        var averageRating = reviewCount > 0 ? (decimal)product.Reviews.Where(r => r.IsActive && !r.IsDeleted).Average(r => r.Rating) : 0;

        // Volume bilgilerini getir
        var volumes = await _productVolumeService.GetCustomerVolumeOptionsAsync(product.Id);
        var defaultVolume = volumes.FirstOrDefault(v => v.IsDefault) ?? volumes.FirstOrDefault();
        var hasVolumeVariants = volumes.Count > 1;

        // Eğer volume varsa, fiyat ve stok bilgilerini volume'dan al
        var finalPrice = product.DiscountedPrice ?? product.Price ?? 0;
        var finalStockQuantity = defaultVolume?.StockQuantity ?? product.StockQuantity;
        var finalInStock = defaultVolume?.InStock ?? (product.StockQuantity > 0);
        var finalOldPrice = product.DiscountedPrice != null ? product.Price : null;
        // Kampanya bilgilerini getir
        var campaigns = await GetProductCampaignsAsync(product.Id, finalPrice);
        var activeBuyXGetYCampaign = campaigns.FirstOrDefault(c => c.IsBuyXGetYFree);
        return new CustomerProductDto
        {
            Id = product.Id,
            Title = product.Name,
            Slug = product.Slug,
            Sku = product.Sku,
            Brand = product.Brand?.Name,
            Barcode = product.Barcode,
            CategoryName = product.Category?.Name,
            CategorySlug = product.Category?.Slug,
            Price = finalPrice,
            OldPrice = finalOldPrice,
            StockQuantity = finalStockQuantity,
            InStock = finalInStock,
            ImgSrc = mainImage?.OriginalPath,
            Description = product.Description,
            ReviewRate = averageRating,
            ReviewCount = reviewCount,
            Images = product.Images.Select(MapToCustomerProductImageDto).ToList(),
            Reviews = product.Reviews.Where(r => r.IsActive && !r.IsDeleted).Select(MapToCustomerProductReviewDto).ToList(),
            AttributeMappings = product.AttributeMappings.Select(MapToCustomerProductAttributeMappingDto).ToList(),
            Volumes = volumes,
            SelectedVolume = defaultVolume,
            HasVolumeVariants = hasVolumeVariants,
            Campaigns = campaigns,
            ActiveBuyXGetYCampaign = activeBuyXGetYCampaign,
            Metadata = product.Seo != null ? MapToCustomerProductMetadataDto(product.Seo) : null
        };
    }

    private async Task<List<CustomerProductCampaignDto>> GetProductCampaignsAsync(Guid productId, decimal productPrice)
    {
        try
        {
            // Ürün için geçerli kampanyaları getir
            var applicableCampaigns = await _campaignCalculationService.GetApplicableProductCampaignsAsync(Guid.Empty, productId);
            var campaigns = new List<CustomerProductCampaignDto>();

            foreach (var applicableCampaign in applicableCampaigns.Where(c => c.CanApply))
            {
                var campaign = await _campaignService.GetByIdAsync(applicableCampaign.CampaignId);
                if (campaign == null) continue;

                var campaignDto = new CustomerProductCampaignDto
                {
                    Id = campaign.Id,
                    Name = campaign.Name,
                    Description = campaign.Description,
                    CampaignType = campaign.CampaignType.ToString(),
                    StartDate = campaign.StartDate,
                    EndDate = campaign.EndDate
                };

                // Buy X Get Y Free kampanyası kontrolü
                var buyXGetYDiscount = campaign.Discounts.FirstOrDefault(d =>
                    d.CalculationType == Core.Enums.DiscountCalculationType.BuyXGetYFree);

                if (buyXGetYDiscount != null)
                {
                    campaignDto.IsBuyXGetYFree = true;
                    campaignDto.BuyQuantity = buyXGetYDiscount.BuyQuantity;
                    campaignDto.GetQuantity = buyXGetYDiscount.GetQuantity;
                    campaignDto.TotalQuantity = (buyXGetYDiscount.BuyQuantity ?? 0) + (buyXGetYDiscount.GetQuantity ?? 0);

                    // Hesaplama
                    var totalAmount = productPrice * campaignDto.TotalQuantity.Value;
                    var discountedAmount = productPrice * campaignDto.BuyQuantity.Value;
                    var savings = productPrice * campaignDto.GetQuantity.Value;

                    campaignDto.OldPrice = totalAmount;
                    campaignDto.NewPrice = discountedAmount;
                    campaignDto.CalculatedSavings = savings;
                    campaignDto.CampaignText = $"{campaignDto.BuyQuantity} Alana {campaignDto.GetQuantity} Bedava";
                }

                campaigns.Add(campaignDto);
            }

            return campaigns;
        }
        catch
        {
            return [];
        }
    }

    private CustomerProductImageDto MapToCustomerProductImageDto(ProductImage image)
    {
        return new CustomerProductImageDto
        {
            Id = (int)image.Id.GetHashCode(),
            ImgSrc = image.OriginalPath,
            Alt = image.AltText
        };
    }

    private CustomerProductReviewDto MapToCustomerProductReviewDto(ProductReview review)
    {
        return new CustomerProductReviewDto
        {
            Id = (int)review.Id.GetHashCode(),
            Name = "Müşteri", // For privacy, don't show real customer names
            Date = review.CreatedAt.ToString("dd.MM.yyyy"),
            Rating = (int)review.Rating,
            Comment = review.Comment ?? ""
        };
    }

    private CustomerProductAttributeMappingDto MapToCustomerProductAttributeMappingDto(ProductAttributeMapping mapping)
    {
        return new CustomerProductAttributeMappingDto
        {
            Id = (int)mapping.Id.GetHashCode(),
            Attribute = mapping.Attribute != null ? new CustomerProductAttributeDto
            {
                Id = (int)mapping.Attribute.Id.GetHashCode(),
                Name = mapping.Attribute.Name,
                ShortName = mapping.Attribute.ShortName,
                IsVariantAttribute = mapping.Attribute.IsVariantAttribute,
                IsListAttribute = mapping.Attribute.IsListAttribute
            } : null,
            AttributeValue = mapping.AttributeValue != null ? new CustomerProductAttributeValueDto
            {
                Id = (int)mapping.AttributeValue.Id.GetHashCode(),
                Value = mapping.AttributeValue.Value
            } : null
        };
    }

    private CustomerProductMetadataDto MapToCustomerProductMetadataDto(ProductSeo seo)
    {
        return new CustomerProductMetadataDto
        {
            Title = seo.MetaTitle ?? "",
            Description = seo.MetaDescription ?? "",
            Keywords = seo.MetaKeywords,
            CanonicalUrl = seo.CanonicalUrl
        };
    }

    private PaginationDto CreatePaginationDto(int page, int pageSize, int totalCount)
    {
        var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

        return new PaginationDto
        {
            Page = page,
            PageSize = pageSize,
            TotalItems = totalCount,
            TotalPages = totalPages,
            HasPreviousPage = page > 1,
            HasNextPage = page < totalPages,
            PreviousPage = page > 1 ? page - 1 : null,
            NextPage = page < totalPages ? page + 1 : null
        };
    }

    private CustomerPaginationDto CreateCustomerPaginationDto(int page, int pageSize, int totalCount)
    {
        var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

        return new CustomerPaginationDto
        {
            Page = page,
            ItemCount = totalCount,
            ItemsPerPage = pageSize,
            TotalPages = totalPages
        };
    }

    private CustomerFiltersDto MapToCustomerFiltersDto(ProductFilterOptionsDto filterOptions)
    {
        return new CustomerFiltersDto
        {
            Categories = filterOptions.Categories.Select(c => new CustomerCategoryFilterDto
            {
                Id = c.Id,
                Name = c.Name,
                Slug = c.Slug,
                Count = c.ProductCount
            }).ToList(),
            Brands = filterOptions.Brands.Select(b => new CustomerBrandFilterDto
            {
                Id = b.Id,
                Name = b.Name,
                Count = b.ProductCount
            }).ToList(),
            Sizes = filterOptions.Sizes.Select(s => new CustomerSizeFilterDto
            {
                Id = s.Id,
                Name = s.Name,
                Count = s.ProductCount
            }).ToList(),
            VariantAttributes = filterOptions.VariantAttributes.Select(va => new CustomerVariantAttributeFilterDto
            {
                Id = va.Id,
                Name = va.Name,
                ShortName = va.ShortName,
                Values = va.Values.Select(v => new CustomerVariantAttributeValueFilterDto
                {
                    Id = v.Id,
                    Value = v.Value,
                    Count = v.ProductCount
                }).ToList()
            }).ToList(),
            Price = new CustomerPriceFilterDto
            {
                MinPrice = filterOptions.PriceRange.MinPrice,
                MaxPrice = filterOptions.PriceRange.MaxPrice
            },
            Availability = new CustomerAvailabilityFilterDto
            {
                InStock = true,
                InStockCount = filterOptions.Availability.InStockCount,
                OutOfStock = true,
                OutOfStockCount = filterOptions.Availability.OutOfStockCount
            }
        };
    }

    // Remaining interface implementations
    public async Task<ProductPageDataDto> SearchProductsAsync(ProductSearchRequestDto request)
    {
        var filter = new ProductFilterDto
        {
            SearchTerm = request.Query,
            PageType = request.PageType,
            CategorySlug = request.CategorySlug,
            CategoryIds = request.Categories,
            BrandIds = request.Brands,
            Sizes = request.Sizes,
            VariantAttributes = request.VariantAttributes,
            PriceMin = request.PriceMin,
            PriceMax = request.PriceMax,
            InStock = request.InStock,
            Page = request.Page,
            PageSize = request.Limit
        };

        if (!string.IsNullOrEmpty(request.Sort))
        {
            var sortParts = request.Sort.Split(':');
            filter.SortBy = sortParts.Length > 0 ? sortParts[0] : "title";
            filter.SortDirection = sortParts.Length > 1 ? sortParts[1] : "asc";
        }

        return await GetProductPageDataAsync(filter);
    }

    public async Task<List<CustomerListProductDto>> SearchProductsSimpleAsync(string query, int count = 12)
    {
        var products = await _productRepository.Query()
            .Include(p => p.Category)
            .Include(p => p.Brand)
            .Include(p => p.Images)
            .Where(p => p.IsActive && !p.IsDeleted && p.ParentProductId == null &&
                       (p.Name.Contains(query) || (p.Description != null && p.Description.Contains(query))))
            .Take(count)
            .ToListAsync();

        return products.Select(MapToCustomerListProductDto).ToList();
    }

    public async Task<ProductFilterOptionsDto> GetFilterOptionsAsync(string? pageType = null, string? categorySlug = null)
    {
        var categories = await _categoryRepository.Query()
            .Where(c => c.IsActive && !c.IsDeleted)
            .Select(c => new FilterCategoryDto
            {
                Id = c.Id,
                Name = c.Name,
                Slug = c.Slug,
                ProductCount = c.Products.Count(p => p.IsActive && !p.IsDeleted)
            })
            .ToListAsync();

        var brands = await _brandRepository.Query()
            .Where(b => b.IsActive && !b.IsDeleted)
            .Select(b => new FilterBrandDto
            {
                Id = b.Id,
                Name = b.Name,
                ProductCount = b.Products.Count(p => p.IsActive && !p.IsDeleted)
            })
            .ToListAsync();

        var variantAttributes = await GetVariantAttributesForFilterAsync(categorySlug);
        var priceRange = await GetPriceRangeAsync();
        var availability = await GetAvailabilityStatsAsync();

        return new ProductFilterOptionsDto
        {
            Categories = categories,
            Brands = brands,
            Sizes = [], // TODO: Implement size filtering
            VariantAttributes = variantAttributes,
            PriceRange = priceRange,
            Availability = availability
        };
    }

    public async Task<PageMetadataDto> GetPageMetadataAsync(PageMetadataRequestDto request)
    {
        return request.PageType switch
        {
            "category" when !string.IsNullOrEmpty(request.CategorySlug) => await GetCategoryMetadataAsync(request.CategorySlug),
            "product" when !string.IsNullOrEmpty(request.ProductSlug) => await GetProductMetadataAsync(request.ProductSlug),
            "new-season" => CreatePageMetadata("Yeni Sezon Ürünleri", "En yeni sezon ürünlerini keşfedin", request.BaseUrl + "/yeni-sezon"),
            "discount" => CreatePageMetadata("İndirimli Ürünler", "En uygun fiyatlı ürünleri kaçırmayın", request.BaseUrl + "/indirim"),
            _ => CreatePageMetadata("Tüm Ürünler", "Geniş ürün yelpazemizi keşfedin", request.BaseUrl + "/urunler")
        };
    }

    public async Task<ProductPageMetadataDto> GetProductMetadataAsync(string slug)
    {
        var product = await _productRepository.Query()
            .Include(p => p.Seo)
            .Include(p => p.Category)
            .Include(p => p.Brand)
            .Include(p => p.Images)
            .FirstOrDefaultAsync(p => p.Slug == slug && p.IsActive && !p.IsDeleted);

        if (product?.Seo == null)
        {
            return new ProductPageMetadataDto
            {
                Title = product?.Name ?? "Ürün Bulunamadı",
                Description = product?.Description ?? "",
                ProductName = product?.Name ?? "",
                ProductPrice = product?.Price,
                ProductBrand = product?.Brand?.Name,
                ProductCategory = product?.Category?.Name,
                ProductInStock = product?.StockQuantity > 0,
                ProductSku = product?.Sku
            };
        }

        var mainImage = product.Images.FirstOrDefault(i => i.IsMainImage) ?? product.Images.FirstOrDefault();

        return new ProductPageMetadataDto
        {
            Title = product.Seo.MetaTitle ?? product.Name,
            Description = product.Seo.MetaDescription ?? product.Description ?? "",
            Keywords = product.Seo.MetaKeywords,
            CanonicalUrl = product.Seo.CanonicalUrl,
            OgTitle = product.Seo.OgTitle ?? product.Name,
            OgDescription = product.Seo.OgDescription ?? product.Description ?? "",
            OgImage = product.Seo.OgImage ?? mainImage?.OriginalPath,
            ProductName = product.Name,
            ProductDescription = product.Description,
            ProductPrice = product.Price,
            ProductImage = mainImage?.OriginalPath,
            ProductBrand = product.Brand?.Name,
            ProductCategory = product.Category?.Name,
            ProductInStock = product.StockQuantity > 0,
            ProductSku = product.Sku
        };
    }

    public async Task<CategoryPageMetadataDto> GetCategoryMetadataAsync(string categorySlug)
    {
        var category = await _categoryRepository.Query()
            .FirstOrDefaultAsync(c => c.Slug == categorySlug && c.IsActive && !c.IsDeleted);

        if (category == null)
        {
            return new CategoryPageMetadataDto
            {
                Title = "Kategori Bulunamadı",
                Description = "",
                CategoryName = "Kategori Bulunamadı",
                ProductCount = 0
            };
        }

        var productCount = await _productRepository.CountAsync(p => p.CategoryId == category.Id && p.IsActive && !p.IsDeleted);

        return new CategoryPageMetadataDto
        {
            Title = $"{category.Name} - Ürünler",
            Description = $"{category.Name} kategorisindeki tüm ürünleri keşfedin",
            CategoryName = category.Name,
            ProductCount = productCount
        };
    }

    // Remaining interface methods
    public async Task<CategoryPageDataDto> GetCategoryPageDataAsync(string categorySlug, ProductFilterDto filter)
    {
        var category = await _categoryRepository.Query()
            .FirstOrDefaultAsync(c => c.Slug == categorySlug && c.IsActive && !c.IsDeleted);

        if (category == null)
        {
            return new CategoryPageDataDto
            {
                Category = new CustomerCategoryDto { Name = "Kategori Bulunamadı", Slug = categorySlug },
                Products = [],
                FilterOptions = new ProductFilterOptionsDto(),
                Pagination = new PaginationDto(),
                Metadata = new CategoryPageMetadataDto { Title = "Kategori Bulunamadı", Description = "", CategoryName = "Kategori Bulunamadı" }
            };
        }

        filter.CategorySlug = categorySlug;
        var productPageData = await GetProductPageDataAsync(filter);
        var categoryDto = new CustomerCategoryDto
        {
            Id = (int)category.Id.GetHashCode(),
            Name = category.Name,
            Slug = category.Slug
        };

        return new CategoryPageDataDto
        {
            Category = categoryDto,
            Products = productPageData.Products,
            FilterOptions = productPageData.FilterOptions,
            Pagination = productPageData.Pagination,
            Metadata = await GetCategoryMetadataAsync(categorySlug),
            TotalProducts = productPageData.TotalProducts
        };
    }

    public async Task<List<CustomerListProductDto>> GetProductsByCategoryAsync(string categorySlug, ProductFilterDto filter)
    {
        filter.CategorySlug = categorySlug;
        var result = await GetProductListAsync(filter);
        return result.Products;
    }

    public async Task<HomePageDataDto> GetHomePageDataAsync()
    {
        var featuredProducts = await GetFeaturedProductsAsync(8);
        var newProducts = await GetNewSeasonProductsAsync(8);
        var discountedProducts = await GetDiscountedProductsAsync(8);

        var featuredCategories = await _categoryRepository.Query()
            .Where(c => c.IsActive && !c.IsDeleted && c.ParentId == null)
            .Take(6)
            .Select(c => new CustomerCategoryDto
            {
                Id = (int)c.Id.GetHashCode(),
                Name = c.Name,
                Slug = c.Slug
            })
            .ToListAsync();

        return new HomePageDataDto
        {
            FeaturedProducts = featuredProducts,
            NewProducts = newProducts,
            DiscountedProducts = discountedProducts,
            FeaturedCategories = featuredCategories,
            Metadata = CreatePageMetadata("Ana Sayfa", "En kaliteli ürünleri keşfedin", "/")
        };
    }

    // Product availability and stock methods
    public async Task<bool> IsProductInStockAsync(Guid productId)
    {
        var product = await _productRepository.GetByIdAsync(productId);
        return product?.StockQuantity > 0;
    }

    public async Task<int> GetProductStockQuantityAsync(Guid productId)
    {
        var product = await _productRepository.GetByIdAsync(productId);
        return product?.StockQuantity ?? 0;
    }

    public async Task<bool> IsProductActiveAsync(Guid productId)
    {
        var product = await _productRepository.GetByIdAsync(productId);
        return product?.IsActive == true && product?.IsDeleted == false;
    }

    // Product variants methods (simplified for now)
    public Task<List<CustomerVariantOptionDto>> GetProductColorsAsync(Guid productId)
    {
        // TODO: Implement color variant logic
        return Task.FromResult(new List<CustomerVariantOptionDto>());
    }

    public Task<List<CustomerVariantOptionDto>> GetProductSizesAsync(Guid productId)
    {
        // TODO: Implement size variant logic
        return Task.FromResult(new List<CustomerVariantOptionDto>());
    }

    public async Task<List<CustomerProductImageDto>> GetProductImagesAsync(Guid productId)
    {
        var images = await _imageRepository.Query()
            .Where(i => i.ProductId == productId)
            .OrderBy(i => i.SortOrder)
            .ToListAsync();

        return images.Select(MapToCustomerProductImageDto).ToList();
    }

    // Analytics methods
    public async Task<int> GetTotalProductCountAsync()
    {
        return await _productRepository.CountAsync(p => !p.IsDeleted);
    }

    public async Task<int> GetActiveProductCountAsync()
    {
        return await _productRepository.CountAsync(p => p.IsActive && !p.IsDeleted);
    }

    public async Task<int> GetInStockProductCountAsync()
    {
        return await _productRepository.CountAsync(p => p.IsActive && !p.IsDeleted && p.StockQuantity > 0);
    }

    public async Task<decimal> GetAverageProductPriceAsync()
    {
        var products = await _productRepository.Query()
            .Where(p => p.IsActive && !p.IsDeleted && p.Price.HasValue)
            .ToListAsync();

        return products.Any() ? products.Average(p => p.Price!.Value) : 0;
    }

    // Helper methods
    private async Task<FilterPriceRangeDto> GetPriceRangeAsync()
    {
        var products = await _productRepository.Query()
            .Where(p => p.IsActive && !p.IsDeleted && p.Price.HasValue)
            .ToListAsync();

        if (!products.Any())
        {
            return new FilterPriceRangeDto { MinPrice = 0, MaxPrice = 0 };
        }

        return new FilterPriceRangeDto
        {
            MinPrice = products.Min(p => p.Price!.Value),
            MaxPrice = products.Max(p => p.Price!.Value)
        };
    }

    private async Task<FilterAvailabilityDto> GetAvailabilityStatsAsync()
    {
        var inStockCount = await _productRepository.CountAsync(p => p.IsActive && !p.IsDeleted && p.StockQuantity > 0);
        var outOfStockCount = await _productRepository.CountAsync(p => p.IsActive && !p.IsDeleted && p.StockQuantity <= 0);

        return new FilterAvailabilityDto
        {
            InStockCount = inStockCount,
            OutOfStockCount = outOfStockCount,
            TotalCount = inStockCount + outOfStockCount
        };
    }

    private async Task<List<FilterVariantAttributeDto>> GetVariantAttributesForFilterAsync(string? categorySlug = null)
    {
        var query = _attributeRepository.Query()
            .Include(a => a.Values)
            .Where(a => a.IsActive && !a.IsDeleted && a.IsVariantAttribute);

        // If category is specified, filter attributes by category
        if (!string.IsNullOrEmpty(categorySlug))
        {
            query = query.Where(a => a.Categories.Any(c => c.Slug == categorySlug && c.IsActive && !c.IsDeleted));
        }

        var attributes = await query.ToListAsync();

        var result = new List<FilterVariantAttributeDto>();

        foreach (var attribute in attributes)
        {
            // Get product count for each attribute value
            var attributeValues = new List<FilterVariantAttributeValueDto>();

            foreach (var value in attribute.Values.Where(v => v.IsActive && !v.IsDeleted))
            {
                var productCount = await _attributeMappingRepository.Query()
                    .Include(m => m.Product)
                        .ThenInclude(p => p.Parent)
                    .Where(m => m.AttributeValueId == value.Id &&
                               m.Product.IsActive && !m.Product.IsDeleted &&
                               (m.Product.ParentProductId == null || m.Product.Parent!.IsActive && !m.Product.Parent.IsDeleted))
                    .Select(m => m.Product.ParentProductId ?? m.ProductId)
                    .Distinct()
                    .CountAsync();

                if (productCount > 0)
                {
                    attributeValues.Add(new FilterVariantAttributeValueDto
                    {
                        Id = value.Id,
                        Value = value.Value,
                        ProductCount = productCount
                    });
                }
            }

            if (attributeValues.Any())
            {
                result.Add(new FilterVariantAttributeDto
                {
                    Id = attribute.Id,
                    Name = attribute.Name,
                    ShortName = attribute.ShortName,
                    Values = attributeValues
                });
            }
        }

        return result;
    }

    private PageMetadataDto CreatePageMetadata(string title, string description, string canonicalUrl)
    {
        return new PageMetadataDto
        {
            Title = title,
            Description = description,
            CanonicalUrl = canonicalUrl,
            OgTitle = title,
            OgDescription = description
        };
    }
}
