"use client";

import { iconFeatures } from "@/data/features";
import { Navigation, Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import Link from "next/link";
import { useContextElement } from "@/context/Context";
import QuantitySelect from "../common/QuantitySelect";
import Image from "next/image";
import { formatTLPrice } from "@/utils/currency";
import { useState } from "react";
import { useCustomerAuth } from "@/hooks/useCustomerAuth";

export default function ShopCart() {
  const { isAuthenticated } = useCustomerAuth();
  const {
    cartProducts,
    totalPrice,

    updateQuantity,
    removeItem,
  } = useContextElement();
  const [isAgreed, setIsAgreed] = useState(false);

  return (
    <div className="flat-spacing-2 pt-0">
      <div className="container">
        <div className="row">
          <div className="col-xl-8">
            <div className="tf-page-cart-main">
              <form className="form-cart" onSubmit={(e) => e.preventDefault()}>
                {cartProducts.length ? (
                  <table className="table-page-cart">
                    <thead>
                      <tr>
                        <th>Ürün</th>
                        <th>Fiyat</th>
                        <th>Miktar</th>
                        <th>Toplam Fiyat</th>
                      </tr>
                    </thead>
                    <tbody>
                      {cartProducts.map((product, i) => (
                        <tr key={i} className="tf-cart-item file-delete">
                          <td className="tf-cart-item_product">
                            <Link
                              href={`/urunler/${product.slug}`}
                              className="img-box"
                            >
                              <Image
                                alt="img-product"
                                src={product.image}
                                width={684}
                                height={972}
                              />
                            </Link>
                            <div className="cart-info">
                              <Link
                                href={`/urunler/${product.slug}`}
                                className="name text-md link fw-medium"
                              >
                                {product.name}
                              </Link>
                              {/* <div className="variants">White / L</div> */}
                              {" "}
                              <span
                                className="remove-cart link remove"
                                onClick={() => removeItem(product.id)}
                              >
                                Kaldır
                              </span>
                            </div>
                          </td>
                          <td className="tf-cart-item_price text-center">
                            <span className="cart-price price-on-sale text-md fw-medium">
                              {formatTLPrice(product.price)}
                            </span>
                          </td>
                          <td
                            className="tf-cart-item_quantity"
                            data-cart-title="Miktar"
                          >
                            <QuantitySelect
                              quantity={product.quantity}
                              setQuantity={(qty) => {
                                updateQuantity(product.id, qty);
                              }}
                            />
                          </td>
                          <td
                            className="tf-cart-item_total text-center"
                            data-cart-title="Toplam Tutar"
                          >
                            <div className="cart-total total-price text-md fw-medium">
                              {formatTLPrice(product.price * product.quantity)}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                ) : (
                  <div className="p-4">
                    Sepetiniz Boş. İhtiyacınız ürünleri sepete ekleyerek alışverişe başlayabilirsiniz! {" "} <br />
                    <Link className="tf-btn btn-dark2 animate-btn mt-3" href="/urunler"  >
                      Ürünleri Keşfet
                    </Link>
                  </div>
                )}
                {/* <div className="check-gift"> */}
                {/*   <input type="checkbox" className="tf-check" id="checkGift" /> */}
                {/*   <label htmlFor="checkGift" className="label text-dark-4"> */}
                {/*     Add gift wrap. Only */}
                {/*     <span className="fw-medium">{formatTLPrice(10)}.</span> (You can choose or not) */}
                {/*   </label> */}
                {/* </div> */}
                {/* <div className="box-ip-discount"> */}
                {/*   <input type="text" placeholder="Discount code" /> */}
                {/*   <button type="button" className="tf-btn radius-6 btn-out-line-dark-2"> */}
                {/*     Apply */}
                {/*   </button> */}
                {/* </div> */}
                {/* <div className="cart-note"> */}
                {/*   <label htmlFor="note" className="text-md fw-medium"> */}
                {/*     Special instructions for seller */}
                {/*   </label> */}
                {/*   <textarea id="note" defaultValue={""} /> */}
                {/* </div> */}
              </form>
              <div className="fl-iconbox wow fadeInUp">
                <Swiper dir="ltr" className="swiper tf-swiper sw-auto"
                  {...{
                    slidesPerView: 1,
                    spaceBetween: 12,
                    speed: 800,
                    observer: true,
                    observeParents: true,
                    slidesPerGroup: 1,
                    pagination: {
                      el: ".sw-pagination-iconbox",
                      clickable: true,
                    },
                    breakpoints: {
                      575: {
                        slidesPerView: 2,
                        spaceBetween: 12,
                        slidesPerGroup: 2,
                      },
                      768: {
                        slidesPerView: 3,
                        spaceBetween: 24,
                        slidesPerGroup: 3,
                      },
                      1200: { slidesPerView: "auto", spaceBetween: 24 },
                    },
                  }}
                  modules={[Pagination, Navigation]}
                >
                  {iconFeatures.map((elm, i) => (
                    <SwiperSlide key={i} className="swiper-slide">
                      <div className="tf-icon-box justify-content-center justify-content-sm-start style-3">
                        <div className="box-icon">
                          <i className={`icon ${elm.iconClass}`} />
                        </div>
                        <div className="content">
                          <div className="title text-uppercase">
                            {elm.title}
                          </div>
                        </div>
                      </div>
                    </SwiperSlide>
                  ))}
                </Swiper>
                <div className="d-flex d-xl-none sw-dot-default sw-pagination-iconbox justify-content-center" />
              </div>
            </div>
          </div>
          <div className="col-xl-4">
            <div className="tf-page-cart-sidebar">

              <form
                onSubmit={(e) => e.preventDefault()}
                className="cart-box checkout-cart-box"
              >
                <div className="cart-head">
                  <div className="total-discount text-xl fw-medium">
                    <span>Sepet Toplamı:</span>
                    <span className="total">{formatTLPrice(totalPrice)}</span>
                  </div>
                  <p className="text-sm text-dark-4">
                    Vergiler ve Kargo Dahildir.
                  </p>
                </div>
                <div className="check-agree">
                  <input
                    type="checkbox"
                    className="tf-check"
                    id="check-agree"
                    checked={isAgreed}
                    onChange={(e) => setIsAgreed(e.target.checked)}
                  />
                  <label htmlFor="check-agree" className="label text-dark-4">
                    <Link href={`/mesafeli-satis-sozlesmesi`} className="text-dark-4 fw-medium text-underline link" target="_blank" >
                      Mesafeli Satış Sözleşmesi
                    </Link>
                    'ni kabul ediyorum.
                  </label>
                </div>
                <div className="checkout-btn">
                  <Link href={`/satin-al`} className="tf-btn btn-dark2 animate-btn w-100"
                    onClick={(e) => {
                      if (!isAgreed) {
                        e.preventDefault();
                        alert("Devam etmek için 'Mesafeli Satış Sözleşmesi'ni' onaylamalısınız.");
                        return;
                      }
                      if (!isAuthenticated) {
                        e.preventDefault();
                        alert("Devam etmek için lütfen giriş yapın veya kayıt olun.");
                        // Giriş modalini açmaya çalışalım
                        (async () => {
                          try {
                            const loginModal = document.getElementById('login');
                            if (loginModal) {
                              const bootstrap = await import('bootstrap');
                              const instance = new bootstrap.Offcanvas(loginModal);
                              instance.show();
                            }
                          } catch { }
                        })();
                      }
                    }}
                  >
                    Satın Al
                  </Link>
                </div>
                <div className="cart-imgtrust">
                  <p className="text-center text-sm text-dark-1">Güvenli Ödeme</p>
                  <div className="cart-list-social">
                    <div className="payment-item">
                    </div>
                    <Image src={"/images/payment/iyzicoileode.png"} width={300} height={100}
                      alt="İyzico İle Güvenli Ödeme"></Image>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
