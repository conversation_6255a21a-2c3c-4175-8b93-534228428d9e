"use client";
import { allProducts } from "@/data/products";
import { openCartModal } from "@/utils/openCartModal";
// import { openWistlistModal } from "@/utlis/openWishlist";

import React, { useEffect } from "react";
import { useContext, useState } from "react";
import { api } from "@/lib/api/client";
import { getSession } from "next-auth/react";
const dataContext = React.createContext();
export const useContextElement = () => {
  return useContext(dataContext);
};

export default function Context({ children }) {
  const [cartProducts, setCartProducts] = useState([]);
  const [wishList, setWishList] = useState([]);
  const [compareItem, setCompareItem] = useState([1, 2, 3]);
  const [quickViewItem, setQuickViewItem] = useState(allProducts[0]);
  const [quickAddItem, setQuickAddItem] = useState(1);
  const [totalPrice, setTotalPrice] = useState(0);

  // Toplamları yeniden hesapla
  useEffect(() => {
    const subtotal = cartProducts.reduce((accumulator, product) => {
      return accumulator + (product.quantity || 0) * (product.price || 0);
    }, 0);
    setTotalPrice(subtotal);
  }, [cartProducts]);

  const isAddedToCartProducts = (id) => {
    return cartProducts.some((elm) => String(elm.id) === String(id));
  };

  // API destekli ekleme
  const addProductToCart = async (product, isModal = true) => {
    try {
      const session = await getSession();

      // client-side ve oturum yok: yerel fallback
      if (!session?.accessToken) {
        if (!isAddedToCartProducts(product.id)) {
          const item = {
            ...allProducts.filter((elm) => elm.id == product.id)[0],
            id: product.id ?? 0,
            quantity: product.qty ?? 1,
            price: product.price ?? 0.0,
            oldPrice: product.oldPrice ?? null,
            image: product.imgSrc,
            name: product.title,
            slug: product.slug,
          };
          setCartProducts((pre) => [...pre, item]);
          if (isModal) openCartModal();
        }
        return;
      }

      // oturum varsa WebApi'ye yaz ve state’i senkronize et
      // 1) önce cart’ı çek (mevcutsa)
      const serverCart = await api.get("/cart");
      const cartId = serverCart?.id;

      const unitPrice = product.price ?? 0.0;
      if (!cartId) return; // cart oluşmadıysa noop (ileride create cart eklenebilir)

      await api.post("/cart/items", {
        cartId,
        productId: product.id,
        quantity: product.qty ?? 1,
        unitPrice,
      });

      // başarılı ise tekrar yükle
      const refreshed = await api.get("/cart");
      const items = refreshed?.items || [];
      console.log(items);
      setCartProducts(
        items.map((i) => ({
          id: i.productId,
          quantity: Number(i.quantity),
          price: Number(i.unitPrice),
          image: i.productImage || "/images/placeholder-product.svg",
          name: i.productName || i.product?.name || "Ürün",
          slug: i.product?.slug || "",
        }))
      );

      if (isModal) openCartModal();
    } catch (e) {
      console.error("addProductToCart error", e);
    }
  };

  const updateQuantity = async (id, qty) => {
    try {
      const session = await getSession();
      if (!session?.accessToken) {
        // yerel fallback
        if (isAddedToCartProducts(id)) {
          let item = cartProducts.filter((elm) => elm.id == id)[0];
          let items = [...cartProducts];
          const itemIndex = items.indexOf(item);
          item.quantity = qty / 1;
          items[itemIndex] = item;
          setCartProducts(items);
        }
        return;
      }

      // Sunucu güncelleme: önce cart’ı al
      const serverCart = await api.get("/cart");
      const cartItem = (serverCart?.items || []).find(
        (x) => String(x.productId) === String(id)
      );
      if (!cartItem) return;

      await api.put("/cart/items", {
        id: cartItem.id,
        quantity: qty,
        unitPrice: cartItem.unitPrice,
      });

      const refreshed = await api.get("/cart");
      const items = refreshed?.items || [];
      setCartProducts(
        items.map((i) => ({
          id: i.productId,
          quantity: Number(i.quantity),
          price: Number(i.unitPrice),
          image: i.productImage || "/images/placeholder-product.svg",
          name: i.productName || i.product?.name || "Ürün",
          slug: i.product?.slug || "",
        }))
      );
    } catch (e) {
      console.error("updateQuantity error", e);
    }
  };

  const addToWishlist = (product) => {
    if (!wishList.some((prod) => prod.id == product.id)) {
      setWishList((pre) => [...pre, product]);
      //openWistlistModal();
    } else {
      setWishList((pre) => pre.filter((elm) => elm.id != product.id));
    }
  };

  const removeFromWishlist = (product) => {
    if (!wishList.some((prod) => prod.id == product.id)) {
      setWishList((pre) => [...pre.filter((elm) => elm.id != product.id)]);
    }
  };
  const addToCompareItem = (id) => {
    if (!compareItem.includes(id)) {
      setCompareItem((pre) => [...pre, id]);
    }
  };
  const removeFromCompareItem = (id) => {
    if (compareItem.includes(id)) {
      setCompareItem((pre) => [...pre.filter((elm) => elm != id)]);
    }
  };
  const isAddedtoWishlist = (id) => {
    if (wishList.includes(id)) {
      return true;
    }
    return false;
  };
  const isAddedtoCompareItem = (id) => {
    if (compareItem.includes(id)) {
      return true;
    }
    return false;
  };
  // İlk yüklemede: oturum varsa WebApi’den hydrate; yoksa localStorage
  useEffect(() => {
    (async () => {
      try {
        const session = await getSession();
        if (session?.accessToken) {
          const refreshed = await api.get("/cart");
          const items = refreshed?.items || [];
          setCartProducts(
            items.map((i) => ({
              id: i.productId,
              quantity: Number(i.quantity),
              price: Number(i.unitPrice),
              image: i.productImage || "/images/placeholder-product.svg",
              name: i.productName || i.product?.name || "Ürün",
              slug: i.product?.slug || "",
            }))
          );
        } else {
          const items = JSON.parse(localStorage.getItem("cartList"));
          if (items?.length) setCartProducts(items);
        }
      } catch (e) {
        console.error("Cart hydrate error", e);
        const items = JSON.parse(localStorage.getItem("cartList"));
        if (items?.length) setCartProducts(items);
      }
    })();
  }, []);

  useEffect(() => {
    localStorage.setItem("cartList", JSON.stringify(cartProducts));
  }, [cartProducts]);
  useEffect(() => {
    const items = JSON.parse(localStorage.getItem("wishlist"));
    if (items?.length) {
      setWishList(items);
    }
  }, []);

  useEffect(() => {
    localStorage.setItem("wishlist", JSON.stringify(wishList));
  }, [wishList]);

  const removeItem = async (id) => {
    try {
      const session = await getSession();
      if (!session?.accessToken) {
        setCartProducts((pre) => [...pre.filter((elm) => String(elm.id) !== String(id))]);
        return;
      }
      const serverCart = await api.get("/cart");
      const cartItem = (serverCart?.items || []).find(
        (x) => String(x.productId) === String(id)
      );
      if (!cartItem) return;
      await api.delete(`/cart/items/${cartItem.id}`);
      const refreshed = await api.get("/cart");
      const items = refreshed?.items || [];
      setCartProducts(
        items.map((i) => ({
          id: i.productId,
          quantity: Number(i.quantity),
          price: Number(i.unitPrice),
          image: i.productImage || "/images/placeholder-product.svg",
          name: i.productName || i.product?.name || "Ürün",
          slug: i.product?.slug || "",
        }))
      );
    } catch (e) {
      console.error("removeItem error", e);
    }
  };

  const contextElement = {
    cartProducts,
    setCartProducts,
    totalPrice,
    addProductToCart,
    removeItem,
    isAddedToCartProducts,
    removeFromWishlist,
    addToWishlist,
    isAddedtoWishlist,
    quickViewItem,
    wishList,
    setQuickViewItem,
    quickAddItem,
    setQuickAddItem,
    addToCompareItem,
    isAddedtoCompareItem,
    removeFromCompareItem,
    compareItem,
    setCompareItem,
    updateQuantity,
  };
  return (
    <dataContext.Provider value={contextElement}>
      {children}
    </dataContext.Provider>
  );
}
